<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title">
                                <i class="fas fa-venus-mars"></i> Gender Analytics Report
                            </h4>
                            <p class="card-text mb-0">Comprehensive gender distribution analysis across roles, branches, grades, and organizational levels.</p>
                        </div>
                        <div>
                            <a href="<?= base_url('reports/hr') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-left me-1"></i> Back to HR Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Male Employees</h6>
                            <h3><?= $genderData['gender_summary']['male'] ?></h3>
                            <p class="mb-0"><?= $genderData['gender_percentages']['male'] ?>% of total</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-male fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Female Employees</h6>
                            <h3><?= $genderData['gender_summary']['female'] ?></h3>
                            <p class="mb-0"><?= $genderData['gender_percentages']['female'] ?>% of total</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-female fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Unspecified</h6>
                            <h3><?= $genderData['gender_summary']['unspecified'] ?></h3>
                            <p class="mb-0"><?= $genderData['gender_percentages']['unspecified'] ?>% of total</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-question fa-3x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender Distribution Charts -->
    <div class="row mb-4">
        <!-- Overall Gender Distribution -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Overall Gender Distribution</strong></div>
                <div class="card-body">
                    <canvas id="overallGenderChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- M&E Evaluators by Gender -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>M&E Evaluators by Gender</strong></div>
                <div class="card-body">
                    <canvas id="evaluatorGenderChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender by Role Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Gender Distribution by Role</strong></div>
                <div class="card-body">
                    <canvas id="genderByRoleChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender by Branch Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Gender Distribution by Branch</strong></div>
                <div class="card-body">
                    <canvas id="genderByBranchChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender by Grade Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Gender Distribution by Grade</strong></div>
                <div class="card-body">
                    <canvas id="genderByGradeChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row mb-4">
        <!-- Gender by Role Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Gender Breakdown by Role</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Role</th>
                                    <th>Male</th>
                                    <th>Female</th>
                                    <th>Unspecified</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($genderData['gender_by_role'] as $role => $genders): ?>
                                <tr>
                                    <td><strong><?= esc(ucfirst($role)) ?></strong></td>
                                    <td><span class="badge bg-info"><?= $genders['male'] ?></span></td>
                                    <td><span class="badge bg-warning"><?= $genders['female'] ?></span></td>
                                    <td><span class="badge bg-secondary"><?= $genders['unspecified'] ?></span></td>
                                    <td><strong><?= $genders['male'] + $genders['female'] + $genders['unspecified'] ?></strong></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gender by Branch Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header"><strong>Gender Breakdown by Branch</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Branch</th>
                                    <th>Male</th>
                                    <th>Female</th>
                                    <th>Unspecified</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($genderData['gender_by_branch'] as $branch => $genders): ?>
                                <tr>
                                    <td><strong><?= esc($branch) ?></strong></td>
                                    <td><span class="badge bg-info"><?= $genders['male'] ?></span></td>
                                    <td><span class="badge bg-warning"><?= $genders['female'] ?></span></td>
                                    <td><span class="badge bg-secondary"><?= $genders['unspecified'] ?></span></td>
                                    <td><strong><?= $genders['male'] + $genders['female'] + $genders['unspecified'] ?></strong></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gender by Grade Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header"><strong>Gender Breakdown by Grade</strong></div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Grade</th>
                                    <th>Male</th>
                                    <th>Female</th>
                                    <th>Unspecified</th>
                                    <th>Total</th>
                                    <th>Male %</th>
                                    <th>Female %</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($genderData['gender_by_grade'] as $grade => $genders): ?>
                                <?php 
                                $total = $genders['male'] + $genders['female'] + $genders['unspecified'];
                                $malePercent = $total > 0 ? round(($genders['male'] / $total) * 100, 1) : 0;
                                $femalePercent = $total > 0 ? round(($genders['female'] / $total) * 100, 1) : 0;
                                ?>
                                <tr>
                                    <td><strong><?= esc($grade) ?></strong></td>
                                    <td><span class="badge bg-info"><?= $genders['male'] ?></span></td>
                                    <td><span class="badge bg-warning"><?= $genders['female'] ?></span></td>
                                    <td><span class="badge bg-secondary"><?= $genders['unspecified'] ?></span></td>
                                    <td><strong><?= $total ?></strong></td>
                                    <td><?= $malePercent ?>%</td>
                                    <td><?= $femalePercent ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Chart.js configuration for Gender Analytics
document.addEventListener('DOMContentLoaded', function() {
    // Overall Gender Distribution Pie Chart
    const overallCtx = document.getElementById('overallGenderChart').getContext('2d');
    new Chart(overallCtx, {
        type: 'doughnut',
        data: {
            labels: ['Male', 'Female', 'Unspecified'],
            datasets: [{
                data: [
                    <?= $chartData['gender_summary']['male'] ?>,
                    <?= $chartData['gender_summary']['female'] ?>,
                    <?= $chartData['gender_summary']['unspecified'] ?>
                ],
                backgroundColor: ['#17a2b8', '#ffc107', '#6c757d'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // M&E Evaluators by Gender
    const evaluatorCtx = document.getElementById('evaluatorGenderChart').getContext('2d');
    new Chart(evaluatorCtx, {
        type: 'bar',
        data: {
            labels: ['Male', 'Female', 'Unspecified'],
            datasets: [{
                label: 'M&E Evaluators',
                data: [
                    <?= $chartData['evaluator_by_gender']['male'] ?>,
                    <?= $chartData['evaluator_by_gender']['female'] ?>,
                    <?= $chartData['evaluator_by_gender']['unspecified'] ?>
                ],
                backgroundColor: ['#17a2b8', '#ffc107', '#6c757d'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Gender by Role Chart
    const roleLabels = <?= json_encode(array_keys($chartData['gender_by_role'])) ?>;
    const roleMaleData = roleLabels.map(role => <?= json_encode($chartData['gender_by_role']) ?>[role].male);
    const roleFemaleData = roleLabels.map(role => <?= json_encode($chartData['gender_by_role']) ?>[role].female);
    const roleUnspecifiedData = roleLabels.map(role => <?= json_encode($chartData['gender_by_role']) ?>[role].unspecified);

    const roleCtx = document.getElementById('genderByRoleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'bar',
        data: {
            labels: roleLabels,
            datasets: [
                {
                    label: 'Male',
                    data: roleMaleData,
                    backgroundColor: '#17a2b8'
                },
                {
                    label: 'Female',
                    data: roleFemaleData,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Unspecified',
                    data: roleUnspecifiedData,
                    backgroundColor: '#6c757d'
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            }
        }
    });

    // Gender by Branch Chart
    const branchLabels = <?= json_encode(array_keys($chartData['gender_by_branch'])) ?>;
    const branchMaleData = branchLabels.map(branch => <?= json_encode($chartData['gender_by_branch']) ?>[branch].male);
    const branchFemaleData = branchLabels.map(branch => <?= json_encode($chartData['gender_by_branch']) ?>[branch].female);

    const branchCtx = document.getElementById('genderByBranchChart').getContext('2d');
    new Chart(branchCtx, {
        type: 'horizontalBar',
        data: {
            labels: branchLabels,
            datasets: [
                {
                    label: 'Male',
                    data: branchMaleData,
                    backgroundColor: '#17a2b8'
                },
                {
                    label: 'Female',
                    data: branchFemaleData,
                    backgroundColor: '#ffc107'
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true,
                    beginAtZero: true
                },
                y: {
                    stacked: true
                }
            }
        }
    });

    // Gender by Grade Chart
    const gradeLabels = <?= json_encode(array_keys($chartData['gender_by_grade'])) ?>;
    const gradeMaleData = gradeLabels.map(grade => <?= json_encode($chartData['gender_by_grade']) ?>[grade].male);
    const gradeFemaleData = gradeLabels.map(grade => <?= json_encode($chartData['gender_by_grade']) ?>[grade].female);

    const gradeCtx = document.getElementById('genderByGradeChart').getContext('2d');
    new Chart(gradeCtx, {
        type: 'bar',
        data: {
            labels: gradeLabels,
            datasets: [
                {
                    label: 'Male',
                    data: gradeMaleData,
                    backgroundColor: '#17a2b8'
                },
                {
                    label: 'Female',
                    data: gradeFemaleData,
                    backgroundColor: '#ffc107'
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
